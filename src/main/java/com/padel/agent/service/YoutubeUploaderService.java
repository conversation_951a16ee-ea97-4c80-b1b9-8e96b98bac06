package com.padel.agent.service;

import io.github.bonigarcia.wdm.WebDriverManager;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.springframework.stereotype.Service;

@Service
public class YoutubeUploaderService {

  public void startUploadFlow() {
    WebDriverManager.chromedriver().setup();
    WebDriver driver = new ChromeDriver();

    try {
      driver.get("https://studio.youtube.com");
      // На этом этапе — ты увидишь YouTube Studio (может быть страница логина)
      Thread.sleep(10000); // Даем 10 сек на ручной логин
    } catch (Exception e) {
      e.printStackTrace();
    } finally {
      // Не закрываем браузер специально — для отладки
      // driver.quit();
    }
  }
}