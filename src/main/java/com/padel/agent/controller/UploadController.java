package com.padel.agent.controller;

import com.padel.agent.service.YoutubeUploaderService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/upload")
public class UploadController {

  private final YoutubeUploaderService youtubeUploaderService;

  @PostMapping
  public ResponseEntity<String> upload() {
    return ResponseEntity.ok("Upload started");
  }
}